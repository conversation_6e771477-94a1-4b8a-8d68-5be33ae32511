import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../utils/app_theme.dart';

class AnalyticsDashboardScreen extends StatefulWidget {
  const AnalyticsDashboardScreen({super.key});

  @override
  State<AnalyticsDashboardScreen> createState() =>
      _AnalyticsDashboardScreenState();
}

class _AnalyticsDashboardScreenState extends State<AnalyticsDashboardScreen> {
  Map<int, int> ratingCounts = {};
  bool isLoading = true;
  String errorMessage = '';
  bool isUsingSampleData = false;

  @override
  void initState() {
    super.initState();
    _loadRatingAnalytics();
  }

  Future<void> _loadRatingAnalytics() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = '';
      });

      // Initialize rating counts
      ratingCounts = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0};

      try {
        // Query Firestore for rating events
        final QuerySnapshot snapshot = await FirebaseFirestore.instance
            .collection('analytics_events')
            .where('event_name', isEqualTo: 'app_rating')
            .where('action', isEqualTo: 'star_selected')
            .get();

        for (var doc in snapshot.docs) {
          final data = doc.data() as Map<String, dynamic>;
          final ratingValue = data['rating_value'] as int?;
          if (ratingValue != null && ratingValue >= 1 && ratingValue <= 5) {
            ratingCounts[ratingValue] = (ratingCounts[ratingValue] ?? 0) + 1;
          }
        }
      } catch (firestoreError) {
        // If Firestore query fails, show sample data for demonstration
        print('Firestore query failed: $firestoreError');
        _loadSampleData();
        isUsingSampleData = true;
      }

      setState(() {
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
        errorMessage = 'Error loading analytics: $e';
      });
    }
  }

  void _loadSampleData() {
    // Load sample data for demonstration purposes
    ratingCounts = {
      5: 15, // 15 five-star ratings
      4: 8, // 8 four-star ratings
      3: 3, // 3 three-star ratings
      2: 2, // 2 two-star ratings
      1: 1, // 1 one-star rating
    };
    print('📊 Loaded sample rating data for demonstration');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Rating Analytics'),
        backgroundColor: AppTheme.primaryBlue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadRatingAnalytics,
          ),
        ],
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : errorMessage.isNotEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: AppTheme.errorColor,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error Loading Data',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 32),
                    child: Text(
                      errorMessage,
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: _loadRatingAnalytics,
                    child: const Text('Retry'),
                  ),
                ],
              ),
            )
          : _buildAnalyticsDashboard(),
    );
  }

  Widget _buildAnalyticsDashboard() {
    final totalRatings = ratingCounts.values.fold(
      0,
      (sum, count) => sum + count,
    );

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Sample data banner
          if (isUsingSampleData)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: AppTheme.warningColor.withValues(alpha: 0.1),
                border: Border.all(color: AppTheme.warningColor),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.info, color: AppTheme.warningColor),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Showing sample data for demonstration. Real data will appear once users start rating the app.',
                      style: TextStyle(color: AppTheme.warningColor),
                    ),
                  ),
                ],
              ),
            ),
          // Summary Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Rating Summary',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Total Ratings: $totalRatings',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  if (totalRatings > 0) ...[
                    Text(
                      'Average Rating: ${_calculateAverageRating().toStringAsFixed(1)} ⭐',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                  ],
                ],
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Rating Breakdown
          Text(
            'Rating Breakdown',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          // Rating bars
          ...List.generate(5, (index) {
            final rating = 5 - index; // Show 5 stars first, then 4, 3, 2, 1
            final count = ratingCounts[rating] ?? 0;
            final percentage = totalRatings > 0
                ? (count / totalRatings) * 100
                : 0;

            return _buildRatingBar(rating, count, percentage.toDouble());
          }),

          const SizedBox(height: 20),

          // Low Rating Focus (1-3 stars)
          _buildLowRatingSection(),
        ],
      ),
    );
  }

  Widget _buildRatingBar(int rating, int count, double percentage) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Star rating
            Row(
              children: List.generate(5, (index) {
                return Icon(
                  index < rating ? Icons.star : Icons.star_border,
                  color: AppTheme.warningColor,
                  size: 20,
                );
              }),
            ),
            const SizedBox(width: 16),

            // Progress bar
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('$count ratings'),
                      Text('${percentage.toStringAsFixed(1)}%'),
                    ],
                  ),
                  const SizedBox(height: 4),
                  LinearProgressIndicator(
                    value: percentage / 100,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(
                      rating >= 4
                          ? AppTheme.successColor
                          : rating == 3
                          ? AppTheme.warningColor
                          : AppTheme.errorColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLowRatingSection() {
    final lowRatingCount =
        (ratingCounts[1] ?? 0) +
        (ratingCounts[2] ?? 0) +
        (ratingCounts[3] ?? 0);
    final totalRatings = ratingCounts.values.fold(
      0,
      (sum, count) => sum + count,
    );
    final lowRatingPercentage = totalRatings > 0
        ? (lowRatingCount / totalRatings) * 100
        : 0;

    return Card(
      color: AppTheme.errorColor.withValues(alpha: 0.1),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.warning, color: AppTheme.errorColor),
                const SizedBox(width: 8),
                Text(
                  'Low Ratings (1-3 ⭐)',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.errorColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              '$lowRatingCount users gave 1-3 star ratings (${lowRatingPercentage.toStringAsFixed(1)}%)',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 8),
            Text(
              'These users need attention to improve their experience.',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: AppTheme.textSecondary),
            ),
            if (lowRatingCount > 0) ...[
              const SizedBox(height: 16),
              Row(
                children: [
                  Text('1⭐: ${ratingCounts[1]} users'),
                  const SizedBox(width: 16),
                  Text('2⭐: ${ratingCounts[2]} users'),
                  const SizedBox(width: 16),
                  Text('3⭐: ${ratingCounts[3]} users'),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  double _calculateAverageRating() {
    int totalRatings = 0;
    int weightedSum = 0;

    ratingCounts.forEach((rating, count) {
      totalRatings += count;
      weightedSum += rating * count;
    });

    return totalRatings > 0 ? weightedSum / totalRatings : 0.0;
  }
}
