import 'package:flutter/material.dart';
import '../services/analytics_service.dart';

/// Test class to simulate rating data for analytics testing
class RatingAnalyticsTest {
  /// Simulate various rating scenarios for testing
  static Future<void> simulateRatingData() async {
    print('🧪 Starting rating analytics simulation...');
    
    // Simulate different rating scenarios
    final scenarios = [
      {'rating': 5, 'count': 15}, // 15 users gave 5 stars
      {'rating': 4, 'count': 8},  // 8 users gave 4 stars
      {'rating': 3, 'count': 3},  // 3 users gave 3 stars
      {'rating': 2, 'count': 2},  // 2 users gave 2 stars
      {'rating': 1, 'count': 1},  // 1 user gave 1 star
    ];
    
    for (var scenario in scenarios) {
      final rating = scenario['rating'] as int;
      final count = scenario['count'] as int;
      
      for (int i = 0; i < count; i++) {
        await AnalyticsService.trackAppRating(
          action: 'star_selected',
          trigger: 'rating_dialog',
          ratingValue: rating,
        );
        
        // Add a small delay to avoid overwhelming the system
        await Future.delayed(const Duration(milliseconds: 100));
      }
      
      print('✅ Simulated $count ratings of $rating stars');
    }
    
    print('🎉 Rating analytics simulation completed!');
    print('📊 Total simulated ratings: 29');
    print('📊 Average rating: ${_calculateSimulatedAverage(scenarios)}');
    print('📊 Low ratings (1-3 stars): 6 users');
    print('📊 High ratings (4-5 stars): 23 users');
  }
  
  static double _calculateSimulatedAverage(List<Map<String, int>> scenarios) {
    int totalRatings = 0;
    int weightedSum = 0;
    
    for (var scenario in scenarios) {
      final rating = scenario['rating']!;
      final count = scenario['count']!;
      totalRatings += count;
      weightedSum += rating * count;
    }
    
    return totalRatings > 0 ? weightedSum / totalRatings : 0.0;
  }
  
  /// Show a test dialog to manually test rating functionality
  static void showTestRatingDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Test Rating Analytics'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Choose a rating to test:'),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: List.generate(5, (index) {
                final rating = index + 1;
                return ElevatedButton(
                  onPressed: () async {
                    await AnalyticsService.trackAppRating(
                      action: 'star_selected',
                      trigger: 'test_dialog',
                      ratingValue: rating,
                    );
                    
                    if (context.mounted) {
                      Navigator.of(context).pop();
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Test rating of $rating stars tracked!'),
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    }
                  },
                  child: Text('$rating⭐'),
                );
              }),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await simulateRatingData();
              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Simulated rating data generated!'),
                    duration: Duration(seconds: 3),
                  ),
                );
              }
            },
            child: const Text('Generate Test Data'),
          ),
        ],
      ),
    );
  }
}
